<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <gradient
                android:startColor="@color/colorAccent"
                android:endColor="@color/colorPrimary"
                android:angle="45" />
            <stroke android:width="4dp" android:color="@color/white" />
            <size android:width="64dp" android:height="64dp" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape android:shape="oval">
            <gradient
                android:startColor="@color/colorAccent"
                android:endColor="@color/colorPrimary"
                android:angle="45" />
            <stroke android:width="3dp" android:color="@color/colorSecondary" />
            <size android:width="64dp" android:height="64dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="oval">
            <gradient
                android:startColor="@color/colorAccent"
                android:endColor="@color/colorPrimary"
                android:angle="45" />
            <stroke android:width="2dp" android:color="@color/white" />
            <size android:width="64dp" android:height="64dp" />
        </shape>
    </item>
</selector>