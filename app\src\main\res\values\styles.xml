<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@color/background</item>
        <item name="android:textColorPrimary">@color/textPrimary</item>
        <item name="android:textColorSecondary">@color/textSecondary</item>
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
        <item name="android:navigationBarColor">@color/colorPrimary</item>
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
        <item name="android:navigationBarColor">@color/colorPrimary</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="AppTheme.NoActionBar.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <!-- Color Button Styles -->
    <style name="ColorButton">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:elevation">4dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:foreground">?android:attr/selectableItemBackgroundBorderless</item>
    </style>

    <style name="ColorButton.Large">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:layout_margin">6dp</item>
        <item name="android:elevation">6dp</item>
    </style>

    <style name="ColorButton.Small">
        <item name="android:layout_width">40dp</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:layout_margin">3dp</item>
        <item name="android:elevation">3dp</item>
    </style>

    <!-- Tool Button Styles -->
    <style name="ToolButton">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:elevation">6dp</item>
        <item name="android:background">@drawable/tool_button_background</item>
        <item name="android:foreground">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:padding">12dp</item>
    </style>

    <!-- Card Styles -->
    <style name="CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardBackgroundColor">@color/background_card</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <style name="CardView.Elevated">
        <item name="cardElevation">8dp</item>
        <item name="cardCornerRadius">16dp</item>
    </style>

    <!-- Text Styles -->
    <style name="TextAppearance.Title" parent="TextAppearance.AppCompat.Title">
        <item name="android:textColor">@color/textPrimary</item>
        <item name="android:textSize">24sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>

    <style name="TextAppearance.Subtitle" parent="TextAppearance.AppCompat.Subhead">
        <item name="android:textColor">@color/textSecondary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <style name="TextAppearance.Body" parent="TextAppearance.AppCompat.Body1">
        <item name="android:textColor">@color/textPrimary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>

    <!-- Floating Action Button Style -->
    <style name="FloatingActionButton">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:layout_margin">16dp</item>
        <item name="android:elevation">6dp</item>
        <item name="android:background">@drawable/fab_background</item>
        <item name="android:foreground">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:padding">16dp</item>
    </style>

</resources>
