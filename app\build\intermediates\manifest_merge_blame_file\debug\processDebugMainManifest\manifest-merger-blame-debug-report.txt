1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.alwan.kids2025"
4    android:versionCode="4"
5    android:versionName="4.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->Z:\alwan3\app\src\main\AndroidManifest.xml:5:5-67
11-->Z:\alwan3\app\src\main\AndroidManifest.xml:5:22-64
12    <!-- Removed WRITE_EXTERNAL_STORAGE permission -->
13    <!-- Removed permissions related to storage and media -->
14    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
14-->Z:\alwan3\app\src\main\AndroidManifest.xml:8:5-76
14-->Z:\alwan3\app\src\main\AndroidManifest.xml:8:22-74
15    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
15-->Z:\alwan3\app\src\main\AndroidManifest.xml:9:5-79
15-->Z:\alwan3\app\src\main\AndroidManifest.xml:9:22-76
16
17    <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
18    <permission
18-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:7:5-9:47
19        android:name="com.alwan.kids2025.permission.C2D_MESSAGE"
19-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:8:9-63
20        android:protectionLevel="signature" />
20-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:9:9-44
21
22    <uses-permission android:name="com.alwan.kids2025.permission.C2D_MESSAGE" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
22-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:11:5-79
22-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:11:22-76
23    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- Required, makes sure notifications are delivered on time. -->
23-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:18:5-82
23-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:18:22-79
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:19:5-68
24-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:19:22-65
25    <!--
26 Required so the device vibrates on receiving a push notification.
27         Vibration settings of the device still apply.
28    -->
29    <uses-permission android:name="android.permission.VIBRATE" />
29-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:24:5-66
29-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:24:22-63
30    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
30-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:25:5-79
30-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:25:22-76
31    <!--
32 Use to restore notifications the user hasn't interacted with.
33         They could be missed notifications if the user reboots their device if this isn't in place.
34    -->
35    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- START: ShortcutBadger -->
35-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:30:5-81
35-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:30:22-78
36    <!-- Samsung -->
37    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
37-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:32:5-86
37-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:32:22-83
38    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
38-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:33:5-87
38-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:33:22-84
39    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
39-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:34:5-81
39-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:34:22-78
40    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
40-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:35:5-83
40-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:35:22-80
41    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
41-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:36:5-88
41-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:36:22-85
42    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
42-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:37:5-92
42-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:37:22-89
43    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
43-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:38:5-84
43-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:38:22-81
44    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
44-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:39:5-83
44-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:39:22-80
45    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
45-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:40:5-91
45-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:40:22-88
46    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
46-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:41:5-92
46-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:41:22-89
47    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
47-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:42:5-93
47-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:42:22-90
48    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
48-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:43:5-73
48-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:43:22-70
49    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
49-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:44:5-82
49-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:44:22-79
50    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
50-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:45:5-83
50-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:45:22-80
51    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
51-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:46:5-88
51-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:46:22-85
52    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
52-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:47:5-89
52-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:47:22-86
53    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
53-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:27:5-82
53-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:27:22-79
54    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
54-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:28:5-88
54-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:28:22-85
55    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
55-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:29:5-83
55-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:29:22-80
56    <queries>
56-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:35:5-51:15
57
58        <!-- For browser content -->
59        <intent>
59-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:38:9-44:18
60            <action android:name="android.intent.action.VIEW" />
60-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:77:17-69
60-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:77:25-66
61
62            <category android:name="android.intent.category.BROWSABLE" />
62-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:41:13-74
62-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:41:23-71
63
64            <data android:scheme="https" />
64-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
64-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:19-41
65        </intent>
66        <!-- End of browser content -->
67        <!-- For CustomTabsService -->
68        <intent>
68-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:47:9-49:18
69            <action android:name="android.support.customtabs.action.CustomTabsService" />
69-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:48:13-90
69-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:48:21-87
70        </intent>
71        <!-- End of CustomTabsService -->
72    </queries>
73
74    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
74-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
74-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
75    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
75-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
75-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
76
77    <permission
77-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0435dc6d01d2f037b2d3a9adb00f5bf6\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
78        android:name="com.alwan.kids2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
78-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0435dc6d01d2f037b2d3a9adb00f5bf6\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
79        android:protectionLevel="signature" />
79-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0435dc6d01d2f037b2d3a9adb00f5bf6\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
80
81    <uses-permission android:name="com.alwan.kids2025.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- To store the heap dumps and leak analysis results. -->
81-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0435dc6d01d2f037b2d3a9adb00f5bf6\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
81-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0435dc6d01d2f037b2d3a9adb00f5bf6\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
82    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
82-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:25:5-80
82-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:25:22-77
83    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
83-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:26:5-81
83-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:26:22-78
84
85    <application
85-->Z:\alwan3\app\src\main\AndroidManifest.xml:12:5-64:19
86        android:allowBackup="false"
86-->Z:\alwan3\app\src\main\AndroidManifest.xml:13:9-36
87        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
87-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0435dc6d01d2f037b2d3a9adb00f5bf6\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
88        android:debuggable="true"
89        android:extractNativeLibs="false"
90        android:icon="@drawable/logo"
90-->Z:\alwan3\app\src\main\AndroidManifest.xml:14:9-38
91        android:label="@string/app_name"
91-->Z:\alwan3\app\src\main\AndroidManifest.xml:15:9-41
92        android:largeHeap="true"
92-->Z:\alwan3\app\src\main\AndroidManifest.xml:17:9-33
93        android:requestLegacyExternalStorage="true"
93-->Z:\alwan3\app\src\main\AndroidManifest.xml:19:9-52
94        android:supportsRtl="true"
94-->Z:\alwan3\app\src\main\AndroidManifest.xml:16:9-35
95        android:testOnly="true"
96        android:theme="@style/AppTheme" >
96-->Z:\alwan3\app\src\main\AndroidManifest.xml:18:9-40
97        <meta-data
97-->Z:\alwan3\app\src\main\AndroidManifest.xml:20:9-100
98            android:name="google_analytics_adid_collection_enabled"
98-->Z:\alwan3\app\src\main\AndroidManifest.xml:20:20-75
99            android:value="false" />
99-->Z:\alwan3\app\src\main\AndroidManifest.xml:20:76-97
100        <!-- Removed duplicate OneSignal App ID meta-data entry -->
101
102        <meta-data
102-->Z:\alwan3\app\src\main\AndroidManifest.xml:23:9-25:69
103            android:name="com.google.android.gms.ads.APPLICATION_ID"
103-->Z:\alwan3\app\src\main\AndroidManifest.xml:24:13-69
104            android:value="ca-app-pub-7841751633097845~3195890380" />
104-->Z:\alwan3\app\src\main\AndroidManifest.xml:25:13-67
105
106        <property
106-->Z:\alwan3\app\src\main\AndroidManifest.xml:26:9-30:48
107            android:name="android.adservices.AD_SERVICES_CONFIG"
107-->Z:\alwan3\app\src\main\AndroidManifest.xml:28:13-65
108            android:resource="@xml/gma_ad_services_config" />
108-->Z:\alwan3\app\src\main\AndroidManifest.xml:29:13-59
109
110        <activity android:name="com.alwan.kids2025.MainActivity" />
110-->Z:\alwan3\app\src\main\AndroidManifest.xml:31:9-67
110-->Z:\alwan3\app\src\main\AndroidManifest.xml:31:19-65
111        <activity
111-->Z:\alwan3\app\src\main\AndroidManifest.xml:32:9-41:20
112            android:name="com.alwan.kids2025.Categories"
112-->Z:\alwan3\app\src\main\AndroidManifest.xml:33:13-57
113            android:exported="true"
113-->Z:\alwan3\app\src\main\AndroidManifest.xml:35:13-36
114            android:theme="@style/AppTheme.NoActionBar" >
114-->Z:\alwan3\app\src\main\AndroidManifest.xml:34:13-56
115            <intent-filter>
115-->Z:\alwan3\app\src\main\AndroidManifest.xml:36:13-40:29
116                <action android:name="android.intent.action.MAIN" />
116-->Z:\alwan3\app\src\main\AndroidManifest.xml:37:17-69
116-->Z:\alwan3\app\src\main\AndroidManifest.xml:37:25-66
117
118                <category android:name="android.intent.category.DEFAULT" />
118-->Z:\alwan3\app\src\main\AndroidManifest.xml:39:17-76
118-->Z:\alwan3\app\src\main\AndroidManifest.xml:39:27-73
119            </intent-filter>
120        </activity>
121        <activity
121-->Z:\alwan3\app\src\main\AndroidManifest.xml:42:9-44:59
122            android:name="com.alwan.kids2025.CategoryItems"
122-->Z:\alwan3\app\src\main\AndroidManifest.xml:43:13-60
123            android:theme="@style/AppTheme.NoActionBar" />
123-->Z:\alwan3\app\src\main\AndroidManifest.xml:44:13-56
124        <activity
124-->Z:\alwan3\app\src\main\AndroidManifest.xml:45:9-54:20
125            android:name="com.alwan.kids2025.Splash"
125-->Z:\alwan3\app\src\main\AndroidManifest.xml:47:13-53
126            android:exported="true"
126-->Z:\alwan3\app\src\main\AndroidManifest.xml:46:13-36
127            android:theme="@style/AppTheme.NoActionBar" >
127-->Z:\alwan3\app\src\main\AndroidManifest.xml:48:13-56
128            <intent-filter>
128-->Z:\alwan3\app\src\main\AndroidManifest.xml:49:13-53:29
129                <action android:name="android.intent.action.MAIN" />
129-->Z:\alwan3\app\src\main\AndroidManifest.xml:37:17-69
129-->Z:\alwan3\app\src\main\AndroidManifest.xml:37:25-66
130
131                <category android:name="android.intent.category.LAUNCHER" />
131-->Z:\alwan3\app\src\main\AndroidManifest.xml:52:17-77
131-->Z:\alwan3\app\src\main\AndroidManifest.xml:52:27-74
132            </intent-filter>
133        </activity>
134
135        <service
135-->Z:\alwan3\app\src\main\AndroidManifest.xml:56:9-63:19
136            android:name="com.alwan.kids2025.MyFirebaseMessagingService"
136-->Z:\alwan3\app\src\main\AndroidManifest.xml:57:13-73
137            android:enabled="true"
137-->Z:\alwan3\app\src\main\AndroidManifest.xml:58:13-35
138            android:exported="true" >
138-->Z:\alwan3\app\src\main\AndroidManifest.xml:59:13-36
139            <intent-filter>
139-->Z:\alwan3\app\src\main\AndroidManifest.xml:60:13-62:29
140                <action android:name="com.google.firebase.MESSAGING_EVENT" />
140-->Z:\alwan3\app\src\main\AndroidManifest.xml:61:17-78
140-->Z:\alwan3\app\src\main\AndroidManifest.xml:61:25-75
141            </intent-filter>
142        </service>
143
144        <receiver
144-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:50:9-61:20
145            android:name="com.onesignal.FCMBroadcastReceiver"
145-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:51:13-62
146            android:exported="true"
146-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:52:13-36
147            android:permission="com.google.android.c2dm.permission.SEND" >
147-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:53:13-73
148
149            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
150            <intent-filter android:priority="999" >
150-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:56:13-60:29
150-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:56:28-50
151                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
151-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:57:17-81
151-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:57:25-78
152
153                <category android:name="com.alwan.kids2025" />
153-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:59:17-61
153-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:59:27-58
154            </intent-filter>
155        </receiver>
156
157        <service
157-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:63:9-69:19
158            android:name="com.onesignal.HmsMessageServiceOneSignal"
158-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:64:13-68
159            android:exported="false" >
159-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:65:13-37
160            <intent-filter>
160-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:66:13-68:29
161                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
161-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:67:17-81
161-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:67:25-78
162            </intent-filter>
163        </service>
164
165        <activity
165-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:71:9-79:20
166            android:name="com.onesignal.NotificationOpenedActivityHMS"
166-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:72:13-71
167            android:exported="true"
167-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:73:13-36
168            android:noHistory="true"
168-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:74:13-37
169            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
169-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:75:13-72
170            <intent-filter>
170-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:76:13-78:29
171                <action android:name="android.intent.action.VIEW" />
171-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:77:17-69
171-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:77:25-66
172            </intent-filter>
173        </activity>
174
175        <service
175-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:81:9-83:40
176            android:name="com.onesignal.FCMIntentService"
176-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:82:13-58
177            android:exported="false" />
177-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:83:13-37
178        <service
178-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:84:9-87:72
179            android:name="com.onesignal.FCMIntentJobService"
179-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:85:13-61
180            android:exported="false"
180-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:86:13-37
181            android:permission="android.permission.BIND_JOB_SERVICE" />
181-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:87:13-69
182        <service
182-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:88:9-91:43
183            android:name="com.onesignal.SyncService"
183-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:89:13-53
184            android:exported="false"
184-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:90:13-37
185            android:stopWithTask="true" />
185-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:91:13-40
186        <service
186-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:92:9-95:72
187            android:name="com.onesignal.SyncJobService"
187-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:93:13-56
188            android:exported="false"
188-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:94:13-37
189            android:permission="android.permission.BIND_JOB_SERVICE" />
189-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:95:13-69
190
191        <activity
191-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:97:9-100:75
192            android:name="com.onesignal.PermissionsActivity"
192-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:98:13-61
193            android:exported="false"
193-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:99:13-37
194            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
194-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:100:13-72
195
196        <receiver
196-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:102:9-104:39
197            android:name="com.onesignal.NotificationDismissReceiver"
197-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:103:13-69
198            android:exported="true" />
198-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:104:13-36
199        <receiver
199-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:105:9-112:20
200            android:name="com.onesignal.BootUpReceiver"
200-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:106:13-56
201            android:exported="true" >
201-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:107:13-36
202            <intent-filter>
202-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:108:13-111:29
203                <action android:name="android.intent.action.BOOT_COMPLETED" />
203-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:109:17-79
203-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:109:25-76
204                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
204-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:110:17-82
204-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:110:25-79
205            </intent-filter>
206        </receiver>
207        <receiver
207-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:113:9-119:20
208            android:name="com.onesignal.UpgradeReceiver"
208-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:114:13-57
209            android:exported="true" >
209-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:115:13-36
210            <intent-filter>
210-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:116:13-118:29
211                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
211-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:117:17-84
211-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:117:25-81
212            </intent-filter>
213        </receiver>
214
215        <activity
215-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:121:9-127:75
216            android:name="com.onesignal.NotificationOpenedReceiver"
216-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:122:13-68
217            android:excludeFromRecents="true"
217-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:123:13-46
218            android:exported="true"
218-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:124:13-36
219            android:noHistory="true"
219-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:125:13-37
220            android:taskAffinity=""
220-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:126:13-36
221            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
221-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:127:13-72
222        <activity
222-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:128:9-133:75
223            android:name="com.onesignal.NotificationOpenedReceiverAndroid22AndOlder"
223-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:129:13-85
224            android:excludeFromRecents="true"
224-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:130:13-46
225            android:exported="true"
225-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:131:13-36
226            android:noHistory="true"
226-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:132:13-37
227            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
227-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:133:13-72
228        <activity
228-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:56:9-61:43
229            android:name="com.google.android.gms.ads.AdActivity"
229-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:57:13-65
230            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
230-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:58:13-122
231            android:exported="false"
231-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:59:13-37
232            android:theme="@android:style/Theme.Translucent" />
232-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:60:13-61
233
234        <provider
234-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:63:9-68:43
235            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
235-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:64:13-76
236            android:authorities="com.alwan.kids2025.mobileadsinitprovider"
236-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:65:13-73
237            android:exported="false"
237-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:66:13-37
238            android:initOrder="100" />
238-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:67:13-36
239
240        <service
240-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:70:9-74:43
241            android:name="com.google.android.gms.ads.AdService"
241-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:71:13-64
242            android:enabled="true"
242-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:72:13-35
243            android:exported="false" />
243-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:73:13-37
244
245        <activity
245-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:76:9-80:43
246            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
246-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:77:13-82
247            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
247-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:78:13-122
248            android:exported="false" />
248-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:79:13-37
249        <activity
249-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:81:9-88:43
250            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
250-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:82:13-82
251            android:excludeFromRecents="true"
251-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:83:13-46
252            android:exported="false"
252-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:84:13-37
253            android:launchMode="singleTask"
253-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:85:13-44
254            android:taskAffinity=""
254-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:86:13-36
255            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
255-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:87:13-72
256
257        <receiver
257-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
258            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
258-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
259            android:exported="true"
259-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
260            android:permission="com.google.android.c2dm.permission.SEND" >
260-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
261            <intent-filter>
261-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
262                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
262-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:57:17-81
262-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:57:25-78
263            </intent-filter>
264
265            <meta-data
265-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
266                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
266-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
267                android:value="true" />
267-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
268        </receiver>
269        <!--
270             FirebaseMessagingService performs security checks at runtime,
271             but set to not exported to explicitly avoid allowing another app to call it.
272        -->
273        <service
273-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
274            android:name="com.google.firebase.messaging.FirebaseMessagingService"
274-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
275            android:directBootAware="true"
275-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
276            android:exported="false" >
276-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
277            <intent-filter android:priority="-500" >
277-->Z:\alwan3\app\src\main\AndroidManifest.xml:60:13-62:29
278                <action android:name="com.google.firebase.MESSAGING_EVENT" />
278-->Z:\alwan3\app\src\main\AndroidManifest.xml:61:17-78
278-->Z:\alwan3\app\src\main\AndroidManifest.xml:61:25-75
279            </intent-filter>
280        </service>
281        <service
281-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:54:9-63:19
282            android:name="com.google.firebase.components.ComponentDiscoveryService"
282-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:55:13-84
283            android:directBootAware="true"
283-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c45f2be2e5a6e5c63463b9206ec70b4a\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:32:13-43
284            android:exported="false" >
284-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:56:13-37
285            <meta-data
285-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
286                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
286-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
287                android:value="com.google.firebase.components.ComponentRegistrar" />
287-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
288            <meta-data
288-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
289                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
289-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
290                android:value="com.google.firebase.components.ComponentRegistrar" />
290-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44e19d41c2b74dc106392c64462b4fa1\transformed\jetified-firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
291            <meta-data
291-->[com.google.firebase:firebase-crashlytics:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\147002f983ab5b188fddbf7317223ab1\transformed\jetified-firebase-crashlytics-18.6.1\AndroidManifest.xml:15:13-17:85
292                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
292-->[com.google.firebase:firebase-crashlytics:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\147002f983ab5b188fddbf7317223ab1\transformed\jetified-firebase-crashlytics-18.6.1\AndroidManifest.xml:16:17-126
293                android:value="com.google.firebase.components.ComponentRegistrar" />
293-->[com.google.firebase:firebase-crashlytics:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\147002f983ab5b188fddbf7317223ab1\transformed\jetified-firebase-crashlytics-18.6.1\AndroidManifest.xml:17:17-82
294            <meta-data
294-->[com.google.firebase:firebase-crashlytics:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\147002f983ab5b188fddbf7317223ab1\transformed\jetified-firebase-crashlytics-18.6.1\AndroidManifest.xml:18:13-20:85
295                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
295-->[com.google.firebase:firebase-crashlytics:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\147002f983ab5b188fddbf7317223ab1\transformed\jetified-firebase-crashlytics-18.6.1\AndroidManifest.xml:19:17-115
296                android:value="com.google.firebase.components.ComponentRegistrar" />
296-->[com.google.firebase:firebase-crashlytics:18.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\147002f983ab5b188fddbf7317223ab1\transformed\jetified-firebase-crashlytics-18.6.1\AndroidManifest.xml:20:17-82
297            <meta-data
297-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f7a9a04ccd838b8ac7360824db9e83b\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
298                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
298-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f7a9a04ccd838b8ac7360824db9e83b\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
299                android:value="com.google.firebase.components.ComponentRegistrar" />
299-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f7a9a04ccd838b8ac7360824db9e83b\transformed\jetified-play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
300            <meta-data
300-->[com.google.firebase:firebase-sessions:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60582d0f4494b57a43cad61425bae6c0\transformed\jetified-firebase-sessions-1.2.1\AndroidManifest.xml:29:13-31:85
301                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
301-->[com.google.firebase:firebase-sessions:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60582d0f4494b57a43cad61425bae6c0\transformed\jetified-firebase-sessions-1.2.1\AndroidManifest.xml:30:17-117
302                android:value="com.google.firebase.components.ComponentRegistrar" />
302-->[com.google.firebase:firebase-sessions:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60582d0f4494b57a43cad61425bae6c0\transformed\jetified-firebase-sessions-1.2.1\AndroidManifest.xml:31:17-82
303            <meta-data
303-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27fdf88a8fdfb5133d7f05f23d6b293\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
304                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
304-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27fdf88a8fdfb5133d7f05f23d6b293\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
305                android:value="com.google.firebase.components.ComponentRegistrar" />
305-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27fdf88a8fdfb5133d7f05f23d6b293\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
306            <meta-data
306-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27fdf88a8fdfb5133d7f05f23d6b293\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
307                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
307-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27fdf88a8fdfb5133d7f05f23d6b293\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
308                android:value="com.google.firebase.components.ComponentRegistrar" />
308-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c27fdf88a8fdfb5133d7f05f23d6b293\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
309            <meta-data
309-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efed72b94a36ce78d255e101a92ea995\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
310                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
310-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efed72b94a36ce78d255e101a92ea995\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
311                android:value="com.google.firebase.components.ComponentRegistrar" />
311-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efed72b94a36ce78d255e101a92ea995\transformed\jetified-firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
312            <meta-data
312-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c45f2be2e5a6e5c63463b9206ec70b4a\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
313                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
313-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c45f2be2e5a6e5c63463b9206ec70b4a\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:36:17-109
314                android:value="com.google.firebase.components.ComponentRegistrar" />
314-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c45f2be2e5a6e5c63463b9206ec70b4a\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:37:17-82
315            <meta-data
315-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92debe17116a085196a77d698d964a20\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:27:13-29:85
316                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
316-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92debe17116a085196a77d698d964a20\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:28:17-115
317                android:value="com.google.firebase.components.ComponentRegistrar" />
317-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92debe17116a085196a77d698d964a20\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:29:17-82
318        </service>
319
320        <activity
320-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\083d3ee68330637059264ac4c231822d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
321            android:name="com.google.android.gms.common.api.GoogleApiActivity"
321-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\083d3ee68330637059264ac4c231822d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:20:19-85
322            android:exported="false"
322-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\083d3ee68330637059264ac4c231822d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:22:19-43
323            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
323-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\083d3ee68330637059264ac4c231822d\transformed\jetified-play-services-base-18.0.1\AndroidManifest.xml:21:19-78
324
325        <service
325-->[com.google.firebase:firebase-sessions:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60582d0f4494b57a43cad61425bae6c0\transformed\jetified-firebase-sessions-1.2.1\AndroidManifest.xml:22:9-25:40
326            android:name="com.google.firebase.sessions.SessionLifecycleService"
326-->[com.google.firebase:firebase-sessions:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60582d0f4494b57a43cad61425bae6c0\transformed\jetified-firebase-sessions-1.2.1\AndroidManifest.xml:23:13-80
327            android:enabled="true"
327-->[com.google.firebase:firebase-sessions:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60582d0f4494b57a43cad61425bae6c0\transformed\jetified-firebase-sessions-1.2.1\AndroidManifest.xml:24:13-35
328            android:exported="false" />
328-->[com.google.firebase:firebase-sessions:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60582d0f4494b57a43cad61425bae6c0\transformed\jetified-firebase-sessions-1.2.1\AndroidManifest.xml:25:13-37
329
330        <provider
330-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c45f2be2e5a6e5c63463b9206ec70b4a\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
331            android:name="com.google.firebase.provider.FirebaseInitProvider"
331-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c45f2be2e5a6e5c63463b9206ec70b4a\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:24:13-77
332            android:authorities="com.alwan.kids2025.firebaseinitprovider"
332-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c45f2be2e5a6e5c63463b9206ec70b4a\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:25:13-72
333            android:directBootAware="true"
333-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c45f2be2e5a6e5c63463b9206ec70b4a\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:26:13-43
334            android:exported="false"
334-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c45f2be2e5a6e5c63463b9206ec70b4a\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:27:13-37
335            android:initOrder="100" />
335-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c45f2be2e5a6e5c63463b9206ec70b4a\transformed\jetified-firebase-common-20.4.2\AndroidManifest.xml:28:13-36
336
337        <receiver
337-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
338            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
338-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
339            android:enabled="true"
339-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
340            android:exported="false" >
340-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
341        </receiver>
342
343        <service
343-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
344            android:name="com.google.android.gms.measurement.AppMeasurementService"
344-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
345            android:enabled="true"
345-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
346            android:exported="false" />
346-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
347        <service
347-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
348            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
348-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
349            android:enabled="true"
349-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
350            android:exported="false"
350-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
351            android:permission="android.permission.BIND_JOB_SERVICE" />
351-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47c3baaa09ac2136ad29870c2061c270\transformed\jetified-play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
352
353        <provider
353-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d7845a43bfc3bab00f4287d28d81e08\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
354            android:name="androidx.startup.InitializationProvider"
354-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d7845a43bfc3bab00f4287d28d81e08\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
355            android:authorities="com.alwan.kids2025.androidx-startup"
355-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d7845a43bfc3bab00f4287d28d81e08\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
356            android:exported="false" >
356-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d7845a43bfc3bab00f4287d28d81e08\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
357            <meta-data
357-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d7845a43bfc3bab00f4287d28d81e08\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
358                android:name="androidx.emoji2.text.EmojiCompatInitializer"
358-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d7845a43bfc3bab00f4287d28d81e08\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
359                android:value="androidx.startup" />
359-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d7845a43bfc3bab00f4287d28d81e08\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
360            <meta-data
360-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
361                android:name="androidx.work.WorkManagerInitializer"
361-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
362                android:value="androidx.startup" />
362-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
363            <meta-data
363-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\250fdfd43744d2c51f193d9b4e77f01a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
364                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
364-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\250fdfd43744d2c51f193d9b4e77f01a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
365                android:value="androidx.startup" />
365-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\250fdfd43744d2c51f193d9b4e77f01a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
366            <meta-data
366-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
367                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
367-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
368                android:value="androidx.startup" />
368-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
369        </provider>
370
371        <uses-library
371-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\450e3c68d4e2b70c7033d218edfb008c\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
372            android:name="androidx.window.extensions"
372-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\450e3c68d4e2b70c7033d218edfb008c\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
373            android:required="false" />
373-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\450e3c68d4e2b70c7033d218edfb008c\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
374        <uses-library
374-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\450e3c68d4e2b70c7033d218edfb008c\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
375            android:name="androidx.window.sidecar"
375-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\450e3c68d4e2b70c7033d218edfb008c\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
376            android:required="false" />
376-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\450e3c68d4e2b70c7033d218edfb008c\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
377
378        <service
378-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
379            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
379-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
380            android:directBootAware="false"
380-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
381            android:enabled="@bool/enable_system_alarm_service_default"
381-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
382            android:exported="false" />
382-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
383        <service
383-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
384            android:name="androidx.work.impl.background.systemjob.SystemJobService"
384-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
385            android:directBootAware="false"
385-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
386            android:enabled="@bool/enable_system_job_service_default"
386-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
387            android:exported="true"
387-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
388            android:permission="android.permission.BIND_JOB_SERVICE" />
388-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
389        <service
389-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
390            android:name="androidx.work.impl.foreground.SystemForegroundService"
390-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
391            android:directBootAware="false"
391-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
392            android:enabled="@bool/enable_system_foreground_service_default"
392-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
393            android:exported="false" />
393-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
394
395        <receiver
395-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
396            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
396-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
397            android:directBootAware="false"
397-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
398            android:enabled="true"
398-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
399            android:exported="false" />
399-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
400        <receiver
400-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
401            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
401-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
402            android:directBootAware="false"
402-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
403            android:enabled="false"
403-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
404            android:exported="false" >
404-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
405            <intent-filter>
405-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
406                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
406-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
407                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
407-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
408            </intent-filter>
409        </receiver>
410        <receiver
410-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
411            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
411-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
412            android:directBootAware="false"
412-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
413            android:enabled="false"
413-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
414            android:exported="false" >
414-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
415            <intent-filter>
415-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
416                <action android:name="android.intent.action.BATTERY_OKAY" />
416-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
416-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
417                <action android:name="android.intent.action.BATTERY_LOW" />
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
417-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
418            </intent-filter>
419        </receiver>
420        <receiver
420-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
421            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
421-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
422            android:directBootAware="false"
422-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
423            android:enabled="false"
423-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
424            android:exported="false" >
424-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
425            <intent-filter>
425-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
426                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
426-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
427                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
427-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
428            </intent-filter>
429        </receiver>
430        <receiver
430-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
431            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
431-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
432            android:directBootAware="false"
432-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
433            android:enabled="false"
433-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
434            android:exported="false" >
434-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
435            <intent-filter>
435-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
436                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
436-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
437            </intent-filter>
438        </receiver>
439        <receiver
439-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
440            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
440-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
441            android:directBootAware="false"
441-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
442            android:enabled="false"
442-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
443            android:exported="false" >
443-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
444            <intent-filter>
444-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
445                <action android:name="android.intent.action.BOOT_COMPLETED" />
445-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:109:17-79
445-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:109:25-76
446                <action android:name="android.intent.action.TIME_SET" />
446-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
446-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
447                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
447-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
447-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
448            </intent-filter>
449        </receiver>
450        <receiver
450-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
451            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
451-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
452            android:directBootAware="false"
452-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
453            android:enabled="@bool/enable_system_alarm_service_default"
453-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
454            android:exported="false" >
454-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
455            <intent-filter>
455-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
456                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
456-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
456-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
457            </intent-filter>
458        </receiver>
459        <receiver
459-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
460            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
460-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
461            android:directBootAware="false"
461-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
462            android:enabled="true"
462-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
463            android:exported="true"
463-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
464            android:permission="android.permission.DUMP" >
464-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
465            <intent-filter>
465-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
466                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
466-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
466-->[androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9309e276b7ee3b11ecfe6d1a4f82be8\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
467            </intent-filter>
468        </receiver>
469
470        <uses-library
470-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b7472ef95b5cc3fd7d6942823f7f381\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
471            android:name="android.ext.adservices"
471-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b7472ef95b5cc3fd7d6942823f7f381\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
472            android:required="false" />
472-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3b7472ef95b5cc3fd7d6942823f7f381\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
473
474        <meta-data
474-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a124e314a86b50e3d298a6c4fbe89657\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
475            android:name="com.google.android.gms.version"
475-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a124e314a86b50e3d298a6c4fbe89657\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
476            android:value="@integer/google_play_services_version" />
476-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a124e314a86b50e3d298a6c4fbe89657\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
477
478        <provider
478-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:32:9-40:20
479            android:name="leakcanary.internal.LeakCanaryFileProvider"
479-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:33:13-70
480            android:authorities="com.squareup.leakcanary.fileprovider.com.alwan.kids2025"
480-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:34:13-88
481            android:exported="false"
481-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:35:13-37
482            android:grantUriPermissions="true" >
482-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:36:13-47
483            <meta-data
483-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:37:13-39:66
484                android:name="android.support.FILE_PROVIDER_PATHS"
484-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:38:17-67
485                android:resource="@xml/leak_canary_file_paths" />
485-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:39:17-63
486        </provider>
487
488        <activity
488-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:42:9-73:20
489            android:name="leakcanary.internal.activity.LeakActivity"
489-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:43:13-69
490            android:exported="true"
490-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:44:13-36
491            android:icon="@mipmap/leak_canary_icon"
491-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:45:13-52
492            android:label="@string/leak_canary_display_activity_label"
492-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:46:13-71
493            android:taskAffinity="com.squareup.leakcanary.com.alwan.kids2025"
493-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:47:13-76
494            android:theme="@style/leak_canary_LeakCanary.Base" >
494-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:48:13-63
495            <intent-filter android:label="@string/leak_canary_import_hprof_file" >
495-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:49:13-72:29
495-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:49:28-81
496                <action android:name="android.intent.action.VIEW" />
496-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:77:17-69
496-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\28fda726727997242fa0de63fd20771c\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:77:25-66
497
498                <category android:name="android.intent.category.DEFAULT" />
498-->Z:\alwan3\app\src\main\AndroidManifest.xml:39:17-76
498-->Z:\alwan3\app\src\main\AndroidManifest.xml:39:27-73
499                <category android:name="android.intent.category.BROWSABLE" />
499-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:41:13-74
499-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:41:23-71
500
501                <data android:scheme="file" />
501-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
501-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:19-41
502                <data android:scheme="content" />
502-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
502-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:19-41
503                <data android:mimeType="*/*" />
503-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
504                <data android:host="*" />
504-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
505                <data android:pathPattern=".*\\.hprof" />
505-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
506                <data android:pathPattern=".*\\..*\\.hprof" />
506-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
507                <data android:pathPattern=".*\\..*\\..*\\.hprof" />
507-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
508                <data android:pathPattern=".*\\..*\\..*\\..*\\.hprof" />
508-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
509                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.hprof" />
509-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
510                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
510-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
511                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
511-->[com.google.android.gms:play-services-ads-lite:22.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af35b5ce038bf23ce446cc551d195678\transformed\jetified-play-services-ads-lite-22.6.0\AndroidManifest.xml:43:13-44
512                <!--
513            Since hprof isn't a standard MIME type, we have to declare such patterns.
514            Most file providers will generate URIs including their own package name,
515            which contains `.` characters that must be explicitly escaped in pathPattern.
516            @see https://stackoverflow.com/a/31028507/703646
517                -->
518            </intent-filter>
519        </activity>
520
521        <activity-alias
521-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:75:9-92:26
522            android:name="leakcanary.internal.activity.LeakLauncherActivity"
522-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:76:13-77
523            android:banner="@drawable/leak_canary_tv_icon"
523-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:77:13-59
524            android:enabled="@bool/leak_canary_add_launcher_icon"
524-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:78:13-66
525            android:exported="true"
525-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:79:13-36
526            android:icon="@mipmap/leak_canary_icon"
526-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:80:13-52
527            android:label="@string/leak_canary_display_activity_label"
527-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:81:13-71
528            android:targetActivity="leakcanary.internal.activity.LeakActivity"
528-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:82:13-79
529            android:taskAffinity="com.squareup.leakcanary.com.alwan.kids2025"
529-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:83:13-76
530            android:theme="@style/leak_canary_LeakCanary.Base" >
530-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:84:13-63
531            <intent-filter>
531-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:85:13-91:29
532                <action android:name="android.intent.action.MAIN" />
532-->Z:\alwan3\app\src\main\AndroidManifest.xml:37:17-69
532-->Z:\alwan3\app\src\main\AndroidManifest.xml:37:25-66
533
534                <category android:name="android.intent.category.LAUNCHER" />
534-->Z:\alwan3\app\src\main\AndroidManifest.xml:52:17-77
534-->Z:\alwan3\app\src\main\AndroidManifest.xml:52:27-74
535                <!-- Android TV launcher intent -->
536                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
536-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:90:17-86
536-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:90:27-83
537            </intent-filter>
538        </activity-alias>
539
540        <activity
540-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:94:9-100:68
541            android:name="leakcanary.internal.RequestPermissionActivity"
541-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:95:13-73
542            android:excludeFromRecents="true"
542-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:96:13-46
543            android:icon="@mipmap/leak_canary_icon"
543-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:97:13-52
544            android:label="@string/leak_canary_storage_permission_activity_label"
544-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:98:13-82
545            android:taskAffinity="com.squareup.leakcanary.com.alwan.kids2025"
545-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:99:13-76
546            android:theme="@style/leak_canary_Theme.Transparent" />
546-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:100:13-65
547
548        <receiver android:name="leakcanary.internal.NotificationReceiver" />
548-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:102:9-77
548-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15559410dbf155a25beae62fee6f1c9d\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:102:19-74
549
550        <provider
550-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f817d15416bdab30efc957dbc8fc059c\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:8:9-12:40
551            android:name="leakcanary.internal.MainProcessAppWatcherInstaller"
551-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f817d15416bdab30efc957dbc8fc059c\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:9:13-78
552            android:authorities="com.alwan.kids2025.leakcanary-installer"
552-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f817d15416bdab30efc957dbc8fc059c\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:10:13-72
553            android:enabled="@bool/leak_canary_watcher_auto_install"
553-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f817d15416bdab30efc957dbc8fc059c\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:11:13-69
554            android:exported="false" />
554-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f817d15416bdab30efc957dbc8fc059c\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:12:13-37
555        <provider
555-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdc6548c5ab9d42f92de5808701530ac\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:8:9-12:40
556            android:name="leakcanary.internal.PlumberInstaller"
556-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdc6548c5ab9d42f92de5808701530ac\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:9:13-64
557            android:authorities="com.alwan.kids2025.plumber-installer"
557-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdc6548c5ab9d42f92de5808701530ac\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:10:13-69
558            android:enabled="@bool/leak_canary_plumber_auto_install"
558-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdc6548c5ab9d42f92de5808701530ac\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:11:13-69
559            android:exported="false" />
559-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bdc6548c5ab9d42f92de5808701530ac\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:12:13-37
560
561        <service
561-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ee13683015984ffc5f545463baaa0f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
562            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
562-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ee13683015984ffc5f545463baaa0f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
563            android:exported="false" >
563-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ee13683015984ffc5f545463baaa0f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
564            <meta-data
564-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ee13683015984ffc5f545463baaa0f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
565                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
565-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ee13683015984ffc5f545463baaa0f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
566                android:value="cct" />
566-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9ee13683015984ffc5f545463baaa0f\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
567        </service>
568
569        <receiver
569-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
570            android:name="androidx.profileinstaller.ProfileInstallReceiver"
570-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
571            android:directBootAware="false"
571-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
572            android:enabled="true"
572-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
573            android:exported="true"
573-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
574            android:permission="android.permission.DUMP" >
574-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
575            <intent-filter>
575-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
576                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
576-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
576-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
577            </intent-filter>
578            <intent-filter>
578-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
579                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
579-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
579-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
580            </intent-filter>
581            <intent-filter>
581-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
582                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
582-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
582-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
583            </intent-filter>
584            <intent-filter>
584-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
585                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
585-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
585-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4346f600483990c43937f94c3605133\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
586            </intent-filter>
587        </receiver>
588
589        <service
589-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8ba42581543eb0c316b44f36e4764d4\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
590            android:name="androidx.room.MultiInstanceInvalidationService"
590-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8ba42581543eb0c316b44f36e4764d4\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
591            android:directBootAware="true"
591-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8ba42581543eb0c316b44f36e4764d4\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
592            android:exported="false" />
592-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8ba42581543eb0c316b44f36e4764d4\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
593        <service
593-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7046f71b829698bfe451b9ebeadc95f7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
594            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
594-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7046f71b829698bfe451b9ebeadc95f7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
595            android:exported="false"
595-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7046f71b829698bfe451b9ebeadc95f7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
596            android:permission="android.permission.BIND_JOB_SERVICE" >
596-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7046f71b829698bfe451b9ebeadc95f7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
597        </service>
598
599        <receiver
599-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7046f71b829698bfe451b9ebeadc95f7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
600            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
600-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7046f71b829698bfe451b9ebeadc95f7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
601            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
601-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7046f71b829698bfe451b9ebeadc95f7\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
602        <activity
602-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e8bc030b46f2eb4ba5fb8b1897ffa81\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
603            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
603-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e8bc030b46f2eb4ba5fb8b1897ffa81\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
604            android:exported="false"
604-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e8bc030b46f2eb4ba5fb8b1897ffa81\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
605            android:stateNotNeeded="true"
605-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e8bc030b46f2eb4ba5fb8b1897ffa81\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
606            android:theme="@style/Theme.PlayCore.Transparent" />
606-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e8bc030b46f2eb4ba5fb8b1897ffa81\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
607    </application>
608
609</manifest>
