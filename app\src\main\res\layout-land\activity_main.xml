<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:layoutDirection="rtl"
    android:orientation="horizontal">

    <!-- Left Side Panel -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="8dp">

        <!-- Toolbar -->
        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/background_card">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <!-- Undo Button -->
                <ImageButton
                    android:id="@+id/undo_button"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_margin="4dp"
                    android:src="@drawable/undo_icon"
                    android:contentDescription="تراجع"
                    android:background="@drawable/tool_button_background" />

                <!-- Save Button -->
                <ImageButton
                    android:id="@+id/save_button"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_margin="4dp"
                    android:src="@drawable/save_icon"
                    android:contentDescription="حفظ"
                    android:background="@drawable/tool_button_background" />

                <!-- Share Button -->
                <ImageButton
                    android:id="@+id/share_button"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_margin="4dp"
                    android:src="@drawable/share_icon"
                    android:contentDescription="مشاركة"
                    android:background="@drawable/tool_button_background" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Color Palette -->
        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/background_card">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="12dp">

                <!-- Color Picker Button -->
                <Button
                    android:id="@+id/select_color"
                    style="@style/ColorButton.Large"
                    android:layout_gravity="center"
                    android:layout_marginBottom="8dp"
                    android:background="@drawable/select_color" />

                <!-- Primary Colors Column -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center">

                    <Button
                        android:id="@+id/red"
                        style="@style/ColorButton.Small"
                        android:background="@drawable/red" />

                    <Button
                        android:id="@+id/blue"
                        style="@style/ColorButton.Small"
                        android:background="@drawable/deep_blue" />

                    <Button
                        android:id="@+id/yellow"
                        style="@style/ColorButton.Small"
                        android:background="@drawable/yellow" />

                    <Button
                        android:id="@+id/green"
                        style="@style/ColorButton.Small"
                        android:background="@drawable/deep_green" />

                    <Button
                        android:id="@+id/orange"
                        style="@style/ColorButton.Small"
                        android:background="@drawable/deep_orange" />

                    <Button
                        android:id="@+id/purple"
                        style="@style/ColorButton.Small"
                        android:background="@drawable/deep_purple" />

                    <Button
                        android:id="@+id/pink"
                        style="@style/ColorButton.Small"
                        android:background="@drawable/light_pink" />

                    <Button
                        android:id="@+id/brown"
                        style="@style/ColorButton.Small"
                        android:background="@drawable/brown" />

                    <Button
                        android:id="@+id/gray"
                        style="@style/ColorButton.Small"
                        android:background="@drawable/gray" />

                    <Button
                        android:id="@+id/black"
                        style="@style/ColorButton.Small"
                        android:background="@drawable/black" />

                    <Button
                        android:id="@+id/white"
                        style="@style/ColorButton.Small"
                        android:background="@drawable/white" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <!-- Drawing Area -->
    <androidx.cardview.widget.CardView
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_margin="8dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/background_card">

        <RelativeLayout
            android:id="@+id/relative_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="8dp"
            android:background="@color/white"
            android:splitMotionEvents="true">

            <ImageView
                android:id="@+id/coringImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:scaleType="centerInside"
                android:visibility="gone" />

        </RelativeLayout>

    </androidx.cardview.widget.CardView>

    <!-- Ad Banner -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_margin="8dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id" />

</LinearLayout>