<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="6dp"
    app:cardBackgroundColor="@color/background_card">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Color Palette Title -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="لوحة الألوان"
            android:textAppearance="@style/TextAppearance.Subtitle"
            android:textAlignment="center"
            android:layout_marginBottom="12dp" />

        <!-- Primary Colors Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/red"
                style="@style/ColorButton"
                android:background="@drawable/red" />

            <Button
                android:id="@+id/blue"
                style="@style/ColorButton"
                android:background="@drawable/deep_blue" />

            <Button
                android:id="@+id/yellow"
                style="@style/ColorButton"
                android:background="@drawable/yellow" />

            <Button
                android:id="@+id/green"
                style="@style/ColorButton"
                android:background="@drawable/deep_green" />

            <Button
                android:id="@+id/orange"
                style="@style/ColorButton"
                android:background="@drawable/deep_orange" />

        </LinearLayout>

        <!-- Secondary Colors Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/purple"
                style="@style/ColorButton"
                android:background="@drawable/deep_purple" />

            <Button
                android:id="@+id/pink"
                style="@style/ColorButton"
                android:background="@drawable/light_pink" />

            <Button
                android:id="@+id/light_blue"
                style="@style/ColorButton"
                android:background="@drawable/light_blue" />

            <Button
                android:id="@+id/light_green"
                style="@style/ColorButton"
                android:background="@drawable/light_green" />

            <Button
                android:id="@+id/brown"
                style="@style/ColorButton"
                android:background="@drawable/brown" />

        </LinearLayout>

        <!-- Neutral Colors Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/black"
                style="@style/ColorButton"
                android:background="@drawable/black" />

            <Button
                android:id="@+id/gray"
                style="@style/ColorButton"
                android:background="@drawable/gray" />

            <Button
                android:id="@+id/white"
                style="@style/ColorButton"
                android:background="@drawable/white" />

            <!-- Color Picker Button -->
            <Button
                android:id="@+id/select_color"
                style="@style/ColorButton.Large"
                android:background="@drawable/select_color"
                android:layout_marginStart="16dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
