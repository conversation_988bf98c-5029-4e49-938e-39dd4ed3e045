<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:layoutDirection="rtl">

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Top App Bar -->
        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            app:elevation="4dp">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/colorPrimary"
                app:title="تطبيق التلوين"
                app:titleTextColor="@color/textWhite" />

        </com.google.android.material.appbar.AppBarLayout>

        <!-- Color Palette -->
        <include layout="@layout/color_palette_layout" />

        <!-- Drawing Area -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_margin="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@color/background_card">

            <RelativeLayout
                android:id="@+id/relative_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="8dp"
                android:background="@color/white"
                android:splitMotionEvents="true">

                <ImageView
                    android:id="@+id/coringImage"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_centerInParent="true"
                    android:scaleType="centerInside"
                    android:visibility="gone" />

                <!-- Progress Indicator -->
                <ProgressBar
                    android:id="@+id/progress_bar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:indeterminateTint="@color/colorPrimary"
                    android:visibility="gone" />

            </RelativeLayout>

        </androidx.cardview.widget.CardView>

        <!-- Ad Banner -->
        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            ads:adSize="SMART_BANNER"
            ads:adUnitId="@string/banner_unit_id" />

    </LinearLayout>

    <!-- Floating Action Buttons -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_undo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:layout_marginBottom="80dp"
        android:src="@drawable/undo_icon"
        android:contentDescription="تراجع"
        app:backgroundTint="@color/colorAccent"
        app:tint="@color/textWhite" />

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_save"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/save_icon"
        android:contentDescription="حفظ"
        app:backgroundTint="@color/colorPrimary"
        app:tint="@color/textWhite" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
