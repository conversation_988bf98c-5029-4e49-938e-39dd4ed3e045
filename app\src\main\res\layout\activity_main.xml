<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ads="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:layoutDirection="rtl"
    android:orientation="vertical">

    <!-- Top Toolbar -->
    <include layout="@layout/toolbar_layout" />

    <!-- Color Palette -->
    <include layout="@layout/color_palette_layout" />

    <!-- Drawing Area -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_margin="8dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/background_card">

        <RelativeLayout
            android:id="@+id/relative_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="8dp"
            android:background="@color/white"
            android:splitMotionEvents="true">

            <ImageView
                android:id="@+id/coringImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:scaleType="centerInside"
                android:visibility="gone" />

        </RelativeLayout>

    </androidx.cardview.widget.CardView>

    <!-- Ad Banner -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_unit_id" />

</LinearLayout>