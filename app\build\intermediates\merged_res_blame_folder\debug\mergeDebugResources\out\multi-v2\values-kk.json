{"logs": [{"outputFile": "com.alwan.kids2025.app-mergeDebugResources-66:/values-kk/values-kk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ff26031da5d2fd1b5a216d773db30782\\transformed\\appcompat-1.6.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,318,428,513,619,738,818,895,986,1079,1174,1268,1368,1461,1556,1653,1744,1835,1916,2021,2124,2222,2329,2435,2535,2701,2796", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "208,313,423,508,614,733,813,890,981,1074,1169,1263,1363,1456,1551,1648,1739,1830,1911,2016,2119,2217,2324,2430,2530,2696,2791,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,536,646,731,837,956,1036,1113,1204,1297,1392,1486,1586,1679,1774,1871,1962,2053,2134,2239,2342,2440,2547,2653,2753,2919,13678", "endColumns": "107,104,109,84,105,118,79,76,90,92,94,93,99,92,94,96,90,90,80,104,102,97,106,105,99,165,94,81", "endOffsets": "426,531,641,726,832,951,1031,1108,1199,1292,1387,1481,1581,1674,1769,1866,1957,2048,2129,2234,2337,2435,2542,2648,2748,2914,3009,13755"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\cd9f957af286ef6d4d5507571832228f\\transformed\\material-1.11.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,427,506,600,688,780,892,974,1038,1133,1203,1266,1373,1438,1505,1566,1633,1695,1749,1863,1922,1983,2037,2112,2238,2326,2416,2558,2630,2703,2840,2929,3010,3067,3123,3189,3260,3337,3423,3503,3575,3651,3732,3802,3902,3989,4061,4152,4245,4319,4394,4486,4538,4620,4686,4770,4856,4918,4982,5045,5114,5218,5322,5416,5516,5577,5637", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,78,93,87,91,111,81,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,89,141,71,72,136,88,80,56,55,65,70,76,85,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83", "endOffsets": "268,346,422,501,595,683,775,887,969,1033,1128,1198,1261,1368,1433,1500,1561,1628,1690,1744,1858,1917,1978,2032,2107,2233,2321,2411,2553,2625,2698,2835,2924,3005,3062,3118,3184,3255,3332,3418,3498,3570,3646,3727,3797,3897,3984,4056,4147,4240,4314,4389,4481,4533,4615,4681,4765,4851,4913,4977,5040,5109,5213,5317,5411,5511,5572,5632,5716"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,68,70,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,145", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3014,3092,3168,3247,3341,4143,4235,4347,6816,6966,7378,7448,7511,7618,7683,7750,7811,7878,7940,7994,8108,8167,8228,8282,8357,8483,8571,8661,8803,8875,8948,9085,9174,9255,9312,9368,9434,9505,9582,9668,9748,9820,9896,9977,10047,10147,10234,10306,10397,10490,10564,10639,10731,10783,10865,10931,11015,11101,11163,11227,11290,11359,11463,11567,11661,11761,11822,13037", "endLines": "5,33,34,35,36,37,45,46,47,68,70,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,145", "endColumns": "12,77,75,78,93,87,91,111,81,63,94,69,62,106,64,66,60,66,61,53,113,58,60,53,74,125,87,89,141,71,72,136,88,80,56,55,65,70,76,85,79,71,75,80,69,99,86,71,90,92,73,74,91,51,81,65,83,85,61,63,62,68,103,103,93,99,60,59,83", "endOffsets": "318,3087,3163,3242,3336,3424,4230,4342,4424,6875,7056,7443,7506,7613,7678,7745,7806,7873,7935,7989,8103,8162,8223,8277,8352,8478,8566,8656,8798,8870,8943,9080,9169,9250,9307,9363,9429,9500,9577,9663,9743,9815,9891,9972,10042,10142,10229,10301,10392,10485,10559,10634,10726,10778,10860,10926,11010,11096,11158,11222,11285,11354,11458,11562,11656,11756,11817,11877,13116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\be808dcfdf4062650d9d56fb02e4b62d\\transformed\\jetified-play-services-basement-18.2.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5415", "endColumns": "163", "endOffsets": "5574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f1a8cebdf235588805d6f74854d945d7\\transformed\\preference-1.2.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,340,483,652,736", "endColumns": "69,85,78,142,168,83,79", "endOffsets": "170,256,335,478,647,731,811"}, "to": {"startLines": "66,69,134,146,156,157,158", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6646,6880,12051,13121,13861,14030,14114", "endColumns": "69,85,78,142,168,83,79", "endOffsets": "6711,6961,12125,13259,14025,14109,14189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4630b1237475cc69c83d8707fc0d9479\\transformed\\browser-1.4.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,259,367", "endColumns": "99,103,107,104", "endOffsets": "150,254,362,467"}, "to": {"startLines": "67,71,72,73", "startColumns": "4,4,4,4", "startOffsets": "6716,7061,7165,7273", "endColumns": "99,103,107,104", "endOffsets": "6811,7160,7268,7373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\465c0647661bd02e09c732e6940d4f07\\transformed\\jetified-play-services-ads-22.6.0\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,250,299,356,425,496,609,688,794,849,962,1021,1135,1223,1266,1360,1396,1434,1485,1568,1609", "endColumns": "50,48,56,68,70,112,78,105,54,112,58,113,87,42,93,35,37,50,82,40,55", "endOffsets": "249,298,355,424,495,608,687,793,848,961,1020,1134,1222,1265,1359,1395,1433,1484,1567,1608,1664"}, "to": {"startLines": "131,132,133,135,136,137,138,139,140,141,142,143,144,147,148,149,150,151,152,153,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11882,11937,11990,12130,12203,12278,12395,12478,12588,12647,12764,12827,12945,13264,13311,13409,13449,13491,13546,13633,14194", "endColumns": "54,52,60,72,74,116,82,109,58,116,62,117,91,46,97,39,41,54,86,44,59", "endOffsets": "11932,11985,12046,12198,12273,12390,12473,12583,12642,12759,12822,12940,13032,13306,13404,13444,13486,13541,13628,13673,14249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a28de868e92df6a446272321a9f2ec8d\\transformed\\jetified-play-services-base-18.0.1\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4429,4532,4685,4811,4917,5057,5183,5306,5579,5744,5850,6007,6136,6289,6446,6509,6568", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "4527,4680,4806,4912,5052,5178,5301,5410,5739,5845,6002,6131,6284,6441,6504,6563,6641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b479cd7247d11ef1a29ae7195e6f88e0\\transformed\\core-1.12.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "38,39,40,41,42,43,44,155", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3429,3524,3626,3728,3831,3935,4032,13760", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "3519,3621,3723,3826,3930,4027,4138,13856"}}]}]}