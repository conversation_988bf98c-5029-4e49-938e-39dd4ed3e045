<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/background_card">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="12dp">

        <!-- Undo Button -->
        <ImageButton
            android:id="@+id/undo_button"
            style="@style/ToolButton"
            android:src="@drawable/undo_icon"
            android:contentDescription="تراجع"
            android:background="@drawable/tool_button_background" />

        <!-- Save Button -->
        <ImageButton
            android:id="@+id/save_button"
            style="@style/ToolButton"
            android:src="@drawable/save_icon"
            android:contentDescription="حفظ"
            android:background="@drawable/tool_button_background" />

        <!-- Share Button -->
        <ImageButton
            android:id="@+id/share_button"
            style="@style/ToolButton"
            android:src="@drawable/share_icon"
            android:contentDescription="مشاركة"
            android:background="@drawable/tool_button_background" />

        <!-- Clear Button -->
        <ImageButton
            android:id="@+id/clear_button"
            style="@style/ToolButton"
            android:src="@android:drawable/ic_menu_revert"
            android:contentDescription="مسح"
            android:background="@drawable/tool_button_background" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
