<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="@color/red" />
            <stroke android:width="4dp" android:color="@color/white" />
            <size android:width="48dp" android:height="48dp" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape android:shape="oval">
            <solid android:color="@color/red" />
            <stroke android:width="3dp" android:color="@color/colorPrimary" />
            <size android:width="48dp" android:height="48dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="oval">
            <solid android:color="@color/red" />
            <stroke android:width="2dp" android:color="@color/white" />
            <size android:width="48dp" android:height="48dp" />
        </shape>
    </item>
</selector>