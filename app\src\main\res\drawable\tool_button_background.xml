<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/colorPrimary" />
            <corners android:radius="16dp" />
            <stroke android:width="2dp" android:color="@color/colorPrimaryDark" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/background_card" />
            <corners android:radius="16dp" />
            <stroke android:width="1dp" android:color="@color/divider" />
        </shape>
    </item>
</selector>
